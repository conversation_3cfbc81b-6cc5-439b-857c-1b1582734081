<?php

use App\Http\Controllers\SuperAdmin\SuperAdminDashboardController;
use App\Http\Controllers\SuperAdmin\SystemManagementController;
use App\Http\Controllers\SuperAdmin\UserManagementController as SuperAdminUserManagementController;
use App\Http\Controllers\SuperAdmin\RoleManagementController;
use App\Http\Controllers\SuperAdmin\ProductController as SuperAdminProductController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Super Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register super admin routes for your application.
| These routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group and require super_admin role.
|
*/

Route::middleware(['auth', 'verified', 'role:super_admin'])->prefix('super-admin')->name('super-admin.')->group(function () {

    // Super Admin Dashboard
    Route::get('/', [SuperAdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard', [SuperAdminDashboardController::class, 'index'])->name('dashboard.alt');
    Route::get('api/data', [SuperAdminDashboardController::class, 'data'])->name('dashboard.data');
    Route::get('api/system-health', [SuperAdminDashboardController::class, 'systemHealth'])->name('dashboard.system-health');

    // System Management
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [SystemManagementController::class, 'index'])->name('index');
        Route::get('settings', [SystemManagementController::class, 'settings'])->name('settings');
        Route::put('settings', [SystemManagementController::class, 'updateSettings'])->name('settings.update');
        Route::get('health', [SystemManagementController::class, 'health'])->name('health');
        Route::get('audit-logs', [SystemManagementController::class, 'auditLogs'])->name('audit-logs');
        Route::get('api/system-info', [SystemManagementController::class, 'systemInfo'])->name('api.system-info');

        // Additional System Routes
        Route::get('payment-gateways', function () { return inertia('super-admin/system/payment-gateways'); })->name('payment-gateways');
        Route::get('modules', function () { return inertia('super-admin/system/modules'); })->name('modules');
        Route::get('shipping-rates', function () { return inertia('super-admin/system/shipping-rates'); })->name('shipping-rates');
        Route::get('tax-classes', function () { return inertia('super-admin/system/tax-classes'); })->name('tax-classes');
        Route::get('coupons', function () { return inertia('super-admin/system/coupons'); })->name('coupons');
        Route::get('mail-templates', function () { return inertia('super-admin/system/mail-templates'); })->name('mail-templates');
        Route::get('permissions-templates', function () { return inertia('super-admin/system/permissions-templates'); })->name('permissions-templates');
        Route::get('user-groups', function () { return inertia('super-admin/system/user-groups'); })->name('user-groups');
        Route::get('api-manager', function () { return inertia('super-admin/system/api-manager'); })->name('api-manager');
        Route::get('download-packages', function () { return inertia('super-admin/system/download-packages'); })->name('download-packages');
        Route::get('download-servers', function () { return inertia('super-admin/system/download-servers'); })->name('download-servers');
        Route::get('ip-blacklist', function () { return inertia('super-admin/system/ip-blacklist'); })->name('ip-blacklist');
        Route::get('cron-tasks', function () { return inertia('super-admin/system/cron-tasks'); })->name('cron-tasks');
        Route::get('s3-dashboard', function () { return inertia('super-admin/system/s3-dashboard'); })->name('s3-dashboard');
        Route::get('software-license', function () { return inertia('super-admin/system/software-license'); })->name('software-license');
    });

    // User Management (Super Admin Level)
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [SuperAdminUserManagementController::class, 'index'])->name('index');
        Route::get('create', [SuperAdminUserManagementController::class, 'create'])->name('create');
        Route::post('/', [SuperAdminUserManagementController::class, 'store'])->name('store');
        Route::get('{user}', [SuperAdminUserManagementController::class, 'show'])->name('show');
        Route::get('{user}/edit', [SuperAdminUserManagementController::class, 'edit'])->name('edit');
        Route::put('{user}', [SuperAdminUserManagementController::class, 'update'])->name('update');
        Route::delete('{user}', [SuperAdminUserManagementController::class, 'destroy'])->name('destroy');
        Route::post('{user}/assign-role', [SuperAdminUserManagementController::class, 'assignRole'])->name('assign-role');
        Route::delete('{user}/remove-role', [SuperAdminUserManagementController::class, 'removeRole'])->name('remove-role');
        Route::post('bulk-action', [SuperAdminUserManagementController::class, 'bulkAction'])->name('bulk-action');
        Route::post('{user}/impersonate', [SuperAdminUserManagementController::class, 'impersonate'])->name('impersonate');
        Route::post('{user}/suspend', [SuperAdminUserManagementController::class, 'suspend'])->name('suspend');
        Route::post('{user}/activate', [SuperAdminUserManagementController::class, 'activate'])->name('activate');
    });

    // Role & Permission Management
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [RoleManagementController::class, 'index'])->name('index');
        Route::get('create', [RoleManagementController::class, 'create'])->name('create');
        Route::post('/', [RoleManagementController::class, 'store'])->name('store');
        Route::get('{role}', [RoleManagementController::class, 'show'])->name('show');
        Route::get('{role}/edit', [RoleManagementController::class, 'edit'])->name('edit');
        Route::put('{role}', [RoleManagementController::class, 'update'])->name('update');
        Route::delete('{role}', [RoleManagementController::class, 'destroy'])->name('destroy');
        Route::post('{role}/permissions', [RoleManagementController::class, 'assignPermissions'])->name('assign-permissions');
        Route::delete('{role}/permissions', [RoleManagementController::class, 'revokePermissions'])->name('revoke-permissions');
    });

    // Permission Management
    Route::prefix('permissions')->name('permissions.')->group(function () {
        Route::get('/', [RoleManagementController::class, 'permissions'])->name('index');
        Route::get('create', [RoleManagementController::class, 'createPermission'])->name('create');
        Route::post('/', [RoleManagementController::class, 'storePermission'])->name('store');
        Route::get('{permission}/edit', [RoleManagementController::class, 'editPermission'])->name('edit');
        Route::put('{permission}', [RoleManagementController::class, 'updatePermission'])->name('update');
        Route::delete('{permission}', [RoleManagementController::class, 'destroyPermission'])->name('destroy');
    });

    // Session Management
    Route::prefix('sessions')->name('sessions.')->group(function () {
        Route::get('/', [SuperAdminUserManagementController::class, 'sessions'])->name('index');
        Route::delete('{session}', [SuperAdminUserManagementController::class, 'destroySession'])->name('destroy');
        Route::post('bulk-destroy', [SuperAdminUserManagementController::class, 'bulkDestroySessions'])->name('bulk-destroy');
    });

    // Products Management
    Route::resource('products', SuperAdminProductController::class);
    Route::post('products/bulk-action', [SuperAdminProductController::class, 'bulkAction'])->name('products.bulk-action');

    // Prestock Management
    Route::prefix('prestock')->name('prestock.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/prestock/index'); })->name('index');
    });

    // Files Management
    Route::prefix('files')->name('files.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/files/index'); })->name('index');
    });

    // Pages Management
    Route::prefix('pages')->name('pages.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/pages/index'); })->name('index');
    });

    // Articles Management
    Route::prefix('articles')->name('articles.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/articles/index'); })->name('index');
    });

    // Announcements Management
    Route::prefix('announcements')->name('announcements.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/announcements/index'); })->name('index');
    });

    // Billing Management
    Route::prefix('billing')->name('billing.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/billing/index'); })->name('index');
        Route::get('orders', function () { return inertia('super-admin/billing/orders'); })->name('orders');
        Route::get('invoices', function () { return inertia('super-admin/billing/invoices'); })->name('invoices');
        Route::get('transactions', function () { return inertia('super-admin/billing/transactions'); })->name('transactions');
    });

    // DataTables Management
    Route::prefix('datatables')->name('datatables.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/datatables/index'); })->name('index');
        Route::get('bill-items', function () { return inertia('super-admin/datatables/bill-items'); })->name('bill-items');
        Route::get('transfers', function () { return inertia('super-admin/datatables/transfers'); })->name('transfers');
        Route::get('products', function () { return inertia('super-admin/datatables/products'); })->name('products');
        Route::get('files', function () { return inertia('super-admin/datatables/files'); })->name('files');
        Route::get('articles', function () { return inertia('super-admin/datatables/articles'); })->name('articles');
        Route::get('reviews', function () { return inertia('super-admin/datatables/reviews'); })->name('reviews');
        Route::get('users-packages', function () { return inertia('super-admin/datatables/users-packages'); })->name('users-packages');
        Route::get('users-downloads', function () { return inertia('super-admin/datatables/users-downloads'); })->name('users-downloads');
        Route::get('download-visitors', function () { return inertia('super-admin/datatables/download-visitors'); })->name('download-visitors');
        Route::get('staff-activity', function () { return inertia('super-admin/datatables/staff-activity'); })->name('staff-activity');
    });

    // Options Management
    Route::prefix('options')->name('options.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/options/index'); })->name('index');
        Route::get('system-options', function () { return inertia('super-admin/options/system-options'); })->name('system-options');
        Route::get('interface-template', function () { return inertia('super-admin/options/interface-template'); })->name('interface-template');
        Route::get('dashboard-template', function () { return inertia('super-admin/options/dashboard-template'); })->name('dashboard-template');
        Route::get('email-smtp', function () { return inertia('super-admin/options/email-smtp'); })->name('email-smtp');
        Route::get('download', function () { return inertia('super-admin/options/download'); })->name('download');
        Route::get('cron-tasks', function () { return inertia('super-admin/options/cron-tasks'); })->name('cron-tasks');
        Route::get('s3-integration', function () { return inertia('super-admin/options/s3-integration'); })->name('s3-integration');
    });

    // Tools Management
    Route::prefix('tools')->name('tools.')->group(function () {
        Route::get('/', function () { return inertia('super-admin/tools/index'); })->name('index');
        Route::get('reports', function () { return inertia('super-admin/tools/reports'); })->name('reports');
        Route::get('bulk-mail', function () { return inertia('super-admin/tools/bulk-mail'); })->name('bulk-mail');
        Route::get('balance-transfer', function () { return inertia('super-admin/tools/balance-transfer'); })->name('balance-transfer');
    });
});
