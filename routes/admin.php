<?php

use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductCategoryController;
use App\Http\Controllers\Admin\DigitalAssetController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group and require admin role.
|
*/

Route::middleware(['auth', 'verified', 'role:admin,super_admin'])->prefix('admin')->name('admin.')->group(function () {

    // Admin Dashboard
    Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard.alt');
    Route::get('api/data', [AdminDashboardController::class, 'data'])->name('dashboard.data');
    Route::get('api/system-health', [AdminDashboardController::class, 'systemHealth'])->name('dashboard.system-health');

    // User Management
    Route::resource('users', UserManagementController::class);
    Route::post('users/{user}/assign-role', [UserManagementController::class, 'assignRole'])->name('users.assign-role');
    Route::delete('users/{user}/remove-role', [UserManagementController::class, 'removeRole'])->name('users.remove-role');
    Route::post('users/bulk-action', [UserManagementController::class, 'bulkAction'])->name('users.bulk-action');

    // Product Management
    Route::resource('products', ProductController::class);
    Route::post('products/bulk-action', [ProductController::class, 'bulkAction'])->name('products.bulk-action');

    // Category Management
    Route::resource('categories', ProductCategoryController::class);
    Route::get('categories-tree', [ProductCategoryController::class, 'tree'])->name('categories.tree');
    Route::post('categories/sort-order', [ProductCategoryController::class, 'updateSortOrder'])->name('categories.sort-order');
    Route::post('categories/bulk-action', [ProductCategoryController::class, 'bulkAction'])->name('categories.bulk-action');

    // Digital Asset Management
    Route::prefix('products/{product}/files')->name('products.files.')->group(function () {
        Route::get('/', [DigitalAssetController::class, 'index'])->name('index');
        Route::get('upload', [DigitalAssetController::class, 'create'])->name('create');
        Route::post('upload', [DigitalAssetController::class, 'store'])->name('store');
        Route::get('{asset}', [DigitalAssetController::class, 'show'])->name('show');
        Route::put('{asset}', [DigitalAssetController::class, 'update'])->name('update');
        Route::delete('{asset}', [DigitalAssetController::class, 'destroy'])->name('destroy');
        Route::get('{asset}/download', [DigitalAssetController::class, 'download'])->name('download');
        Route::post('bulk-action', [DigitalAssetController::class, 'bulkAction'])->name('bulk-action');
    });

    // S3 Connection Test
    Route::get('s3/test-connection', [DigitalAssetController::class, 'testConnection'])->name('s3.test-connection');

    // Download Token Management
    Route::post('download-tokens/{asset}/generate', [\App\Http\Controllers\SecureDownloadController::class, 'generateToken'])
        ->name('download-tokens.generate');
    Route::get('download-tokens/{asset}/stats', [\App\Http\Controllers\SecureDownloadController::class, 'getDownloadStats'])
        ->name('download-tokens.stats');
    Route::delete('download-tokens/{asset}/revoke', [\App\Http\Controllers\SecureDownloadController::class, 'revokeTokens'])
        ->name('download-tokens.revoke');
    Route::post('download-tokens/cleanup', [\App\Http\Controllers\SecureDownloadController::class, 'cleanupExpiredTokens'])
        ->name('download-tokens.cleanup');
});
