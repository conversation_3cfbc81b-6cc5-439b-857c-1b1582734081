<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('api/dashboard/data', [App\Http\Controllers\DashboardController::class, 'data'])->name('dashboard.data');
    Route::get('api/dashboard/activities', [App\Http\Controllers\DashboardController::class, 'activities'])->name('dashboard.activities');
    Route::get('api/dashboard/transactions', [App\Http\Controllers\DashboardController::class, 'transactions'])->name('dashboard.transactions');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

Route::get('email-verified', function () {
    return Inertia::render('auth/email-verified');
})->middleware('auth')->name('verification.success');

// Secure download routes
Route::get('/download/{token}', [App\Http\Controllers\SecureDownloadController::class, 'download'])
    ->name('secure.download');

require __DIR__.'/super-admin.php';
require __DIR__.'/admin.php';

// Two-Factor Authentication Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('two-factor', [App\Http\Controllers\TwoFactorController::class, 'show'])->name('two-factor.show');
    Route::post('two-factor/enable', [App\Http\Controllers\TwoFactorController::class, 'enable'])->name('two-factor.enable');
    Route::delete('two-factor/disable', [App\Http\Controllers\TwoFactorController::class, 'disable'])->name('two-factor.disable');
    Route::post('two-factor/recovery-codes', [App\Http\Controllers\TwoFactorController::class, 'generateRecoveryCodes'])->name('two-factor.recovery-codes');
    Route::post('two-factor/generate-secret', [App\Http\Controllers\TwoFactorController::class, 'generateSecret'])->name('two-factor.generate-secret');
});

Route::middleware('guest')->group(function () {
    Route::get('two-factor/challenge', [App\Http\Controllers\TwoFactorController::class, 'challenge'])->name('two-factor.challenge');
    Route::post('two-factor/verify', [App\Http\Controllers\TwoFactorController::class, 'verify'])->name('two-factor.verify');
});
