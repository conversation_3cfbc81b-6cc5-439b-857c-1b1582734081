<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar',
        'status',
        'password',
        'email_verification_token',
        'password_reset_token',
        'password_reset_expires_at',
        'last_login_at',
        'last_login_ip',
        'failed_login_attempts',
        'locked_until',
        'two_factor_enabled',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'password_reset_token',
        'email_verification_token',
        'two_factor_secret',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'password_reset_expires_at' => 'datetime',
            'last_login_at' => 'datetime',
            'locked_until' => 'datetime',
            'two_factor_enabled' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uuid)) {
                $user->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the roles for the user.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
                    ->withPivot('assigned_at', 'assigned_by');

    }

    /**
     * Get the products created by this user.
     */
    public function createdProducts(): HasMany
    {
        return $this->hasMany(Product::class, 'created_by');
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * Check if user has any of the given roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        return $this->roles()->whereIn('name', $roles)->exists();
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->roles()
                    ->whereHas('permissions', function ($query) use ($permissionName) {
                        $query->where('name', $permissionName);
                    })
                    ->exists();
    }

    /**
     * Get user's full name.
     */
    public function getFullNameAttribute(): string
    {
        if ($this->first_name && $this->last_name) {
            return $this->first_name . ' ' . $this->last_name;
        }
        
        return $this->name ?? '';
    }

    /**
     * Check if user account is locked.
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
}
    /**
     * Check if user is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->isLocked();
    }

    /**
     * Two-Factor Authentication Methods
     */

    /**
     * Generate a new two-factor authentication secret.
     */
    public function generateTwoFactorSecret(): string
    {
        $google2fa = new \PragmaRX\Google2FA\Google2FA();
        $secret = $google2fa->generateSecretKey();
        
        $this->update(['two_factor_secret' => $secret]);
        
        return $secret;
    }

    /**
     * Get the two-factor authentication QR code URL.
     */
    public function getTwoFactorQrCodeUrl(): string
    {
        $google2fa = new \PragmaRX\Google2FA\Google2FA();
        $companyName = config('app.name');
        $companyEmail = $this->email;
        
        return $google2fa->getQRCodeUrl(
            $companyName,
            $companyEmail,
            $this->two_factor_secret
        );
    }

    /**
     * Verify a two-factor authentication code.
     */
    public function verifyTwoFactorCode(string $code): bool
    {
        $google2fa = new \PragmaRX\Google2FA\Google2FA();
        
        return $google2fa->verifyKey($this->two_factor_secret, $code);
    }

    /**
     * Enable two-factor authentication.
     */
    public function enableTwoFactorAuth(): void
    {
        $this->update([
            'two_factor_enabled' => true,
            'two_factor_confirmed_at' => now(),
        ]);
    }

    /**
     * Disable two-factor authentication.
     */
    public function disableTwoFactorAuth(): void
    {
        $this->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
        ]);
    }

    /**
     * Generate recovery codes.
     */
    public function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = \Illuminate\Support\Str::random(10);
        }
        
        $this->update(['two_factor_recovery_codes' => json_encode($codes)]);
        
        return $codes;
    }

    /**
     * Get recovery codes.
     */
    public function getRecoveryCodes(): array
    {
        return $this->two_factor_recovery_codes 
            ? json_decode($this->two_factor_recovery_codes, true) 
            : [];
    }

    /**
     * Use a recovery code.
     */
    public function useRecoveryCode(string $code): bool
    {
        $codes = $this->getRecoveryCodes();
        
        if (in_array($code, $codes)) {
            $codes = array_diff($codes, [$code]);
            $this->update(['two_factor_recovery_codes' => json_encode(array_values($codes))]);
            return true;
        }
        
        return false;
    }

    /**
     * Check if two-factor authentication is enabled.
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->two_factor_enabled && !is_null($this->two_factor_secret);
    }
}
